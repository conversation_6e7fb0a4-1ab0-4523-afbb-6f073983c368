-- Test script to verify the arc rendering fix
-- This should now show proper arcs instead of full circles

-- Set up the engine properly (this is normally done by ADekoDebugMode.lua)
local engine = require("makerjs_engine")
ADekoLib.engine = engine

function modelMain()
  G = ADekoLib

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end

  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()

  -- Enable arc debugging
  G.debug_arcs = true

  print("=== Arc Fix Verification Test ===")
  print("Testing both JavaScript and Lua arc fixes...")

  -- Test 1: Arc from 0 to 90 degrees (should show quarter circle)
  print("Test 1: Quarter arc (0° to 90°)")
  G.set<PERSON>ayer("TEST_quarter_arc")
  G.setThickness(-5)
  G.arc({100, 100}, 50, 0, 90, false, "quarter_arc")

  -- Test 2: Arc from 0 to 180 degrees (should show half circle)
  print("Test 2: Half arc (0° to 180°)")
  G<PERSON>set<PERSON>ayer("TEST_half_arc")
  G.setThickness(-5)
  G.arc({250, 100}, 50, 0, 180, false, "half_arc")

  -- Test 3: Arc from 45 to 135 degrees (should show 90-degree arc)
  print("Test 3: 90° arc (45° to 135°)")
  G.setLayer("TEST_45_135_arc")
  G.setThickness(-5)
  G.arc({400, 100}, 50, 45, 135, false, "arc_45_135")

  -- Test 4: Small arc from 0 to 30 degrees (critical test - 0° end angle)
  print("Test 4: Small arc (0° to 30°) - Critical test for 0° end angle")
  G.setLayer("TEST_small_arc")
  G.setThickness(-5)
  G.arc({100, 250}, 50, 0, 30, false, "small_arc")

  -- Test 5: Arc ending at 0 degrees (this was the main bug!)
  print("Test 5: Arc ending at 0° (270° to 0°) - Main bug test")
  G.setLayer("TEST_end_zero")
  G.setThickness(-5)
  G.arc({250, 250}, 50, 270, 0, false, "arc_end_zero")

  -- Test 6: Clockwise arc
  print("Test 6: Clockwise arc (0° to 90°)")
  G.setLayer("TEST_clockwise_arc")
  G.setThickness(-5)
  G.arc({400, 250}, 50, 0, 90, true, "clockwise_arc")

  -- Test 7: Line with bulge (should create arc)
  print("Test 7: Line with bulge")
  G.setLayer("TEST_line_bulge")
  G.setThickness(-5)
  G.line({50, 400}, {150, 400}, 0.5)

  -- Test 8: Line with negative bulge (clockwise arc)
  print("Test 8: Line with negative bulge (clockwise)")
  G.setLayer("TEST_line_neg_bulge")
  G.setThickness(-5)
  G.line({200, 400}, {300, 400}, -0.5)

  -- Test 9: Polyline with arc segment
  print("Test 9: Polyline with arc segment")
  G.setLayer("TEST_polyline_arc")
  G.setThickness(-5)
  local p1 = {350, 400, 0, 0.3}  -- bulge from this point to next
  local p2 = {450, 400, 0, 0}    -- no bulge from this point
  G.polyline(p1, p2)

  -- Test 10: Your original problematic pattern (simplified)
  print("Test 10: Original pattern simulation")
  G.setLayer("TEST_original_pattern")
  G.setThickness(-5)

  local point1 = {50, 500}
  local point2 = {50, 600}
  local point3 = {150, 500}
  local point4 = {150, 600}
  local point10 = {100, 550}  -- middle point

  local bulge_val = G.bulge(point1, point10, point2)
  print("Calculated bulge:", bulge_val)

  -- Create separate points for the polyline
  local arc_p1 = {point1[1], point1[2], 0, bulge_val}
  local arc_p2 = {point2[1], point2[2], 0, 0}

  G.polyline(arc_p1, arc_p2)

  -- Test 11: Complex polyline with multiple arcs
  print("Test 11: Complex polyline with multiple arcs")
  G.setLayer("TEST_complex_polyline")
  G.setThickness(-5)

  local complex_points = {
    {200, 500, 0, 0},      -- Start point, no arc
    {250, 500, 0, 0.3},    -- Arc from this point to next (bulge 0.3)
    {300, 530, 0, -0.4},   -- Arc from this point to next (bulge -0.4, clockwise)
    {350, 500, 0, 0.2},    -- Arc from this point to next (bulge 0.2)
    {400, 500, 0, 0}       -- End point, no arc
  }

  G.polyline(complex_points[1], complex_points[2], complex_points[3], complex_points[4], complex_points[5])

  print("=== Test Complete - Check 2D visualization for proper arcs ===")
  print("All arcs should appear as partial arcs, NOT full circles!")

  return true
end

require "ADekoDebugMode"
