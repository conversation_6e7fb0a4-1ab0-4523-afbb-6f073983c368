use dxf::entities::*;
use dxf::{Drawing, DxfResult, Point, Color, LwPolylineVertex, LineWeight};
use dxf::enums::AcadVersion;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrawCommand {
    pub command_type: String,
    pub x1: f64,
    pub y1: f64,
    pub x2: f64,
    pub y2: f64,
    pub z1: Option<f64>, // Z coordinate for start point of 3D lines
    pub z2: Option<f64>, // Z coordinate for end point of 3D lines
    pub radius: f64,
    pub color: String,
    pub size: f64,
    pub text: String,
    pub layer_name: String,
    pub thickness: Option<f64>, // Thickness/extrusion for 3D representation
    pub start_angle: Option<f64>,
    pub end_angle: Option<f64>,
    pub clockwise: Option<bool>,
    pub svg_path: Option<String>,
    pub points: Option<Vec<Vec<f64>>>, // For polylines: [x, y, z, bulge] format
}

// Tolerance for point comparison (to handle floating point precision issues)
const POINT_TOLERANCE: f64 = 1e-6;

#[derive(Debug, Clone)]
struct ShapeEndpoint {
    x: f64,
    y: f64,
    z: Option<f64>,
}

impl ShapeEndpoint {
    fn new(x: f64, y: f64, z: Option<f64>) -> Self {
        Self { x, y, z }
    }

    fn distance_to(&self, other: &ShapeEndpoint) -> f64 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        let dz = self.z.unwrap_or(0.0) - other.z.unwrap_or(0.0);
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    fn is_close_to(&self, other: &ShapeEndpoint) -> bool {
        self.distance_to(other) < POINT_TOLERANCE
    }
}

#[derive(Debug, Clone)]
struct JoinableShape {
    command: DrawCommand,
    start_point: ShapeEndpoint,
    end_point: ShapeEndpoint,
    is_closed: bool,
}

impl JoinableShape {
    fn from_draw_command(command: &DrawCommand) -> Option<Self> {
        match command.command_type.as_str() {
            "line" => {
                let start = ShapeEndpoint::new(command.x1, command.y1, command.z1);
                let end = ShapeEndpoint::new(command.x2, command.y2, command.z2);
                Some(JoinableShape {
                    command: command.clone(),
                    start_point: start,
                    end_point: end,
                    is_closed: false,
                })
            }
            "polyline" => {
                if let Some(points) = &command.points {
                    if points.len() >= 2 {
                        let first_point = &points[0];
                        let last_point = &points[points.len() - 1];

                        let start = ShapeEndpoint::new(
                            first_point[0],
                            first_point[1],
                            if first_point.len() >= 3 { Some(first_point[2]) } else { None }
                        );
                        let end = ShapeEndpoint::new(
                            last_point[0],
                            last_point[1],
                            if last_point.len() >= 3 { Some(last_point[2]) } else { None }
                        );

                        // Check if polyline is closed (first and last points are the same)
                        let is_closed = start.is_close_to(&end);

                        Some(JoinableShape {
                            command: command.clone(),
                            start_point: start,
                            end_point: end,
                            is_closed,
                        })
                    } else {
                        None
                    }
                } else {
                    None
                }
            }
            "arc" => {
                // For arcs, calculate start and end points from center, radius, and angles
                if let (Some(start_angle), Some(end_angle)) = (command.start_angle, command.end_angle) {
                    let center_x = command.x1;
                    let center_y = command.y1;
                    let radius = command.radius;
                    let z = command.z1;

                    let start_rad = start_angle.to_radians();
                    let end_rad = end_angle.to_radians();

                    let start = ShapeEndpoint::new(
                        center_x + radius * start_rad.cos(),
                        center_y + radius * start_rad.sin(),
                        z
                    );
                    let end = ShapeEndpoint::new(
                        center_x + radius * end_rad.cos(),
                        center_y + radius * end_rad.sin(),
                        z
                    );

                    Some(JoinableShape {
                        command: command.clone(),
                        start_point: start,
                        end_point: end,
                        is_closed: false,
                    })
                } else {
                    None
                }
            }
            _ => None, // Circles, rectangles, and text are not joinable to preserve their geometry
        }
    }

    fn can_join_to(&self, other: &JoinableShape) -> bool {
        if self.is_closed || other.is_closed {
            return false; // Closed shapes cannot be joined
        }

        // Check if any endpoint of this shape is close to any endpoint of the other shape
        self.start_point.is_close_to(&other.start_point) ||
        self.start_point.is_close_to(&other.end_point) ||
        self.end_point.is_close_to(&other.start_point) ||
        self.end_point.is_close_to(&other.end_point)
    }
}

/// Post-processes draw commands to join shapes that share common points and layers
pub fn post_process_draw_commands(commands: &[DrawCommand]) -> Vec<DrawCommand> {
    let mut processed_commands = Vec::new();
    let mut commands_by_layer: HashMap<String, Vec<DrawCommand>> = HashMap::new();

    // Group commands by layer
    for command in commands {
        commands_by_layer
            .entry(command.layer_name.clone())
            .or_insert_with(Vec::new)
            .push(command.clone());
    }

    // Process each layer separately
    for (_layer_name, layer_commands) in commands_by_layer {
        let joined_commands = join_shapes_in_layer(&layer_commands);
        processed_commands.extend(joined_commands);
    }

    processed_commands
}

fn join_shapes_in_layer(commands: &[DrawCommand]) -> Vec<DrawCommand> {
    let mut result = Vec::new();
    let mut joinable_shapes = Vec::new();

    // Separate joinable and non-joinable commands
    for command in commands {
        if let Some(shape) = JoinableShape::from_draw_command(command) {
            joinable_shapes.push(shape);
        } else {
            // Non-joinable commands (circles, rectangles, text) are added as-is
            result.push(command.clone());
        }
    }

    // Join shapes that share common points
    let joined_shapes = join_connected_shapes(joinable_shapes);

    // Convert joined shapes back to draw commands
    for shape_group in joined_shapes {
        if shape_group.len() == 1 {
            // Single shape, add as-is
            result.push(shape_group[0].command.clone());
        } else {
            // Multiple shapes joined together, create a polyline
            let joined_polyline = create_joined_polyline(&shape_group);
            result.push(joined_polyline);
        }
    }

    result
}

fn join_connected_shapes(shapes: Vec<JoinableShape>) -> Vec<Vec<JoinableShape>> {
    let mut result = Vec::new();
    let mut used = HashSet::new();

    for (i, _shape) in shapes.iter().enumerate() {
        if used.contains(&i) {
            continue;
        }

        // Build a connected group starting from this shape
        let connected_group = build_connected_chain(&shapes, i, &mut used);
        if !connected_group.is_empty() {
            result.push(connected_group);
        }
    }

    result
}

fn build_connected_chain(shapes: &[JoinableShape], start_idx: usize, used: &mut HashSet<usize>) -> Vec<JoinableShape> {
    if used.contains(&start_idx) {
        return Vec::new();
    }

    let mut chain = vec![shapes[start_idx].clone()];
    used.insert(start_idx);

    // Build chain in both directions from the starting shape
    extend_chain_forward(&mut chain, shapes, used);
    extend_chain_backward(&mut chain, shapes, used);

    chain
}

fn extend_chain_forward(chain: &mut Vec<JoinableShape>, shapes: &[JoinableShape], used: &mut HashSet<usize>) {
    loop {
        let last_shape = chain.last().unwrap();
        let mut found_connection = false;

        for (i, candidate) in shapes.iter().enumerate() {
            if used.contains(&i) {
                continue;
            }

            // Check if candidate connects to the end of our current chain
            if can_connect_at_end(last_shape, candidate) {
                chain.push(candidate.clone());
                used.insert(i);
                found_connection = true;
                break;
            }
        }

        if !found_connection {
            break;
        }
    }
}

fn extend_chain_backward(chain: &mut Vec<JoinableShape>, shapes: &[JoinableShape], used: &mut HashSet<usize>) {
    loop {
        let first_shape = &chain[0];
        let mut found_connection = false;

        for (i, candidate) in shapes.iter().enumerate() {
            if used.contains(&i) {
                continue;
            }

            // Check if candidate connects to the start of our current chain
            if can_connect_at_start(first_shape, candidate) {
                chain.insert(0, candidate.clone());
                used.insert(i);
                found_connection = true;
                break;
            }
        }

        if !found_connection {
            break;
        }
    }
}

fn can_connect_at_end(current_shape: &JoinableShape, candidate: &JoinableShape) -> bool {
    if current_shape.is_closed || candidate.is_closed {
        return false;
    }

    // Check if candidate's start connects to current shape's end
    current_shape.end_point.is_close_to(&candidate.start_point) ||
    // Or if candidate's end connects to current shape's end (candidate would need to be reversed)
    current_shape.end_point.is_close_to(&candidate.end_point)
}

fn can_connect_at_start(current_shape: &JoinableShape, candidate: &JoinableShape) -> bool {
    if current_shape.is_closed || candidate.is_closed {
        return false;
    }

    // Check if candidate's end connects to current shape's start
    current_shape.start_point.is_close_to(&candidate.end_point) ||
    // Or if candidate's start connects to current shape's start (candidate would need to be reversed)
    current_shape.start_point.is_close_to(&candidate.start_point)
}

fn create_joined_polyline(shapes: &[JoinableShape]) -> DrawCommand {
    if shapes.is_empty() {
        panic!("Cannot create polyline from empty shapes");
    }

    let mut points = Vec::new();
    let first_shape = &shapes[0];

    // Add points from the first shape
    add_shape_points_to_polyline(&mut points, first_shape, false);

    // Add remaining shapes, ensuring proper connectivity
    for i in 1..shapes.len() {
        let current_shape = &shapes[i];
        let needs_reversal = should_reverse_shape(&points, current_shape);

        // Skip the first point if it connects to the last point of the current polyline
        let skip_first = if !points.is_empty() {
            let last_point = &points[points.len() - 1];
            let last_endpoint = ShapeEndpoint::new(
                last_point[0],
                last_point[1],
                if last_point.len() >= 3 { Some(last_point[2]) } else { None }
            );

            let shape_first_point = if needs_reversal { &current_shape.end_point } else { &current_shape.start_point };
            last_endpoint.is_close_to(shape_first_point)
        } else {
            false
        };

        add_shape_points_to_polyline(&mut points, current_shape, needs_reversal);

        // Remove duplicate point if we're connecting at the same location
        if skip_first && points.len() > 1 {
            // Find the duplicate point and preserve any bulge value
            let last_idx = points.len() - 1;
            let shape_points_count = get_shape_points_count(current_shape);
            if shape_points_count > 0 {
                let duplicate_idx = last_idx - shape_points_count + 1;
                if duplicate_idx < points.len() && duplicate_idx > 0 {
                    // Before removing the duplicate, check if it has a bulge value
                    let duplicate_point = &points[duplicate_idx];
                    if duplicate_point.len() >= 4 && duplicate_point[3].abs() > 1e-10 {
                        // Transfer the bulge value to the previous point
                        if points[duplicate_idx - 1].len() >= 4 {
                            points[duplicate_idx - 1][3] = duplicate_point[3];
                        }
                    }
                    points.remove(duplicate_idx);
                }
            }
        }
    }

    DrawCommand {
        command_type: "polyline".to_string(),
        x1: 0.0,
        y1: 0.0,
        x2: 0.0,
        y2: 0.0,
        z1: None,
        z2: None,
        radius: 0.0,
        color: first_shape.command.color.clone(),
        size: first_shape.command.size,
        text: String::new(),
        layer_name: first_shape.command.layer_name.clone(),
        thickness: first_shape.command.thickness,
        start_angle: None,
        end_angle: None,
        clockwise: None,
        svg_path: None,
        points: Some(points),
    }
}

fn add_shape_points_to_polyline(points: &mut Vec<Vec<f64>>, shape: &JoinableShape, reverse: bool) {
    match shape.command.command_type.as_str() {
        "line" => {
            if reverse {
                points.push(vec![shape.end_point.x, shape.end_point.y, shape.end_point.z.unwrap_or(0.0), 0.0]);
                points.push(vec![shape.start_point.x, shape.start_point.y, shape.start_point.z.unwrap_or(0.0), 0.0]);
            } else {
                points.push(vec![shape.start_point.x, shape.start_point.y, shape.start_point.z.unwrap_or(0.0), 0.0]);
                points.push(vec![shape.end_point.x, shape.end_point.y, shape.end_point.z.unwrap_or(0.0), 0.0]);
            }
        }
        "polyline" => {
            if let Some(shape_points) = &shape.command.points {
                let mut pts = shape_points.clone();
                if reverse {
                    pts.reverse();
                    // When reversing, we need to negate bulge values and move them to the previous vertex
                    for i in 0..pts.len() {
                        if pts[i].len() >= 4 {
                            pts[i][3] = -pts[i][3]; // Negate bulge for reverse direction
                        }
                    }

                    // Shift bulge values: bulge at vertex i should apply to segment from i to i+1
                    for i in (1..pts.len()).rev() {
                        if pts[i].len() >= 4 && pts[i-1].len() >= 4 {
                            let bulge = pts[i][3];
                            pts[i][3] = pts[i-1][3];
                            pts[i-1][3] = bulge;
                        }
                    }
                }
                points.extend(pts);
            }
        }
        "arc" => {
            // Convert arc to polyline points with bulge to preserve curvature
            if let (Some(start_angle), Some(end_angle)) = (shape.command.start_angle, shape.command.end_angle) {
                let center_x = shape.command.x1;
                let center_y = shape.command.y1;
                let radius = shape.command.radius;

                let start_rad = start_angle.to_radians();
                let end_rad = end_angle.to_radians();

                let start_x = center_x + radius * start_rad.cos();
                let start_y = center_y + radius * start_rad.sin();
                let end_x = center_x + radius * end_rad.cos();
                let end_y = center_y + radius * end_rad.sin();

                // Calculate bulge value for the arc
                // Bulge = tan(included_angle / 4)
                let mut included_angle = end_angle - start_angle;

                // Normalize angle to handle crossing 0/360 degrees
                while included_angle > 180.0 {
                    included_angle -= 360.0;
                }
                while included_angle < -180.0 {
                    included_angle += 360.0;
                }

                let bulge = (included_angle.to_radians() / 4.0).tan();

                if reverse {
                    // For reversed arc, swap start and end points and negate bulge
                    points.push(vec![end_x, end_y, shape.command.z1.unwrap_or(0.0), -bulge]);
                    points.push(vec![start_x, start_y, shape.command.z1.unwrap_or(0.0), 0.0]);
                } else {
                    points.push(vec![start_x, start_y, shape.command.z1.unwrap_or(0.0), bulge]);
                    points.push(vec![end_x, end_y, shape.command.z1.unwrap_or(0.0), 0.0]);
                }
            }
        }
        _ => {}
    }
}

fn should_reverse_shape(current_points: &[Vec<f64>], shape: &JoinableShape) -> bool {
    if current_points.is_empty() {
        return false;
    }

    let last_point = &current_points[current_points.len() - 1];
    let last_endpoint = ShapeEndpoint::new(
        last_point[0],
        last_point[1],
        if last_point.len() >= 3 { Some(last_point[2]) } else { None }
    );

    // Check if the shape's end point is closer to our current end than the start point
    last_endpoint.is_close_to(&shape.end_point)
}

fn get_shape_points_count(shape: &JoinableShape) -> usize {
    match shape.command.command_type.as_str() {
        "line" => 2,
        "arc" => 2,
        "polyline" => {
            shape.command.points.as_ref().map(|p| p.len()).unwrap_or(0)
        }
        _ => 0,
    }
}

pub struct DxfExporter {
    drawing: Drawing,
    layer_colors: HashMap<String, Color>,
}

impl DxfExporter {
    pub fn new() -> Self {
        let mut drawing = Drawing::new();
        drawing.header.version = AcadVersion::R2018;
        
        // Set up default header values
        drawing.header.current_layer = "0".to_string();
        
        Self {
            drawing,
            layer_colors: HashMap::new(),
        }
    }

    pub fn add_draw_commands(&mut self, commands: &[DrawCommand]) -> DxfResult<()> {
        // First pass: collect all unique layers and set up layer table
        self.setup_layers(commands)?;
        
        // Second pass: convert draw commands to DXF entities
        for command in commands {
            self.add_draw_command(command)?;
        }
        
        Ok(())
    }

    fn setup_layers(&mut self, commands: &[DrawCommand]) -> DxfResult<()> {
        let mut unique_layers = std::collections::HashSet::new();
        
        // Collect unique layer names
        for command in commands {
            unique_layers.insert(command.layer_name.clone());
        }
        
        // Add layers to the drawing
        for layer_name in unique_layers {
            let mut layer = dxf::tables::Layer::default();
            layer.name = layer_name.clone();

            // Set layer color based on layer name or use default
            let color = self.get_layer_color(&layer_name);
            layer.color = color.clone();
            self.layer_colors.insert(layer_name.clone(), color);

            // Set layer line weight if needed
            layer.line_weight = LineWeight::by_layer();

            self.drawing.add_layer(layer);
        }
        
        Ok(())
    }

    fn get_layer_color(&self, layer_name: &str) -> Color {
        // Map common layer names to standard AutoCAD colors
        match layer_name.to_lowercase().as_str() {
            "cut" | "cutting" => Color::from_index(1), // Red
            "engrave" | "engraving" => Color::from_index(2), // Yellow
            "score" | "scoring" => Color::from_index(3), // Green
            "fold" | "folding" => Color::from_index(4), // Cyan
            "drill" | "drilling" => Color::from_index(5), // Blue
            "mill" | "milling" => Color::from_index(6), // Magenta
            "outline" => Color::from_index(7), // White/Black
            "construction" => Color::from_index(8), // Gray
            _ => Color::from_index(7), // Default to white/black
        }
    }

    fn add_draw_command(&mut self, command: &DrawCommand) -> DxfResult<()> {
        match command.command_type.as_str() {
            "line" => self.add_line(command)?,
            "rectangle" => self.add_rectangle(command)?,
            "circle" => self.add_circle(command)?,
            "arc" => self.add_arc(command)?,
            "polyline" => self.add_polyline(command)?,
            "text" => self.add_text(command)?,
            _ => {
                // For unknown command types, try to add as a line if coordinates are available
                if command.x1 != command.x2 || command.y1 != command.y2 {
                    self.add_line(command)?;
                }
            }
        }
        Ok(())
    }

    fn add_line(&mut self, command: &DrawCommand) -> DxfResult<()> {
        let mut line = Line::default();

        // Use separate Z coordinates for 3D lines if provided, otherwise use thickness or 0
        let z1 = command.z1.unwrap_or(command.thickness.unwrap_or(0.0));
        let z2 = command.z2.unwrap_or(command.thickness.unwrap_or(0.0));

        line.p1 = Point::new(command.x1, command.y1, z1);
        line.p2 = Point::new(command.x2, command.y2, z2);

        // Set thickness for extrusion if provided (separate from Z coordinates)
        if let Some(thickness) = command.thickness {
            line.thickness = thickness;
        }

        // Create entity with common properties
        let mut entity = Entity::new(EntityType::Line(line));
        entity.common.layer = command.layer_name.clone();

        // Set color from layer
        if let Some(color) = self.layer_colors.get(&command.layer_name) {
            entity.common.color = color.clone();
        }

        self.drawing.add_entity(entity);
        Ok(())
    }

    fn add_rectangle(&mut self, command: &DrawCommand) -> DxfResult<()> {
        // Create rectangle as a polyline (closed)
        let mut polyline = LwPolyline::default();

        // Set thickness if provided
        if let Some(thickness) = command.thickness {
            polyline.thickness = thickness;
        }

        // Add rectangle vertices
        let x1 = command.x1;
        let y1 = command.y1;
        let x2 = command.x2;
        let y2 = command.y2;

        if command.radius > 0.0 {
            // Rounded rectangle - add vertices with bulge for corners
            let radius = command.radius;
            let bulge = (std::f64::consts::PI / 8.0).tan(); // 45 degree arc bulge

            // Add vertices for rounded rectangle
            polyline.vertices.push(LwPolylineVertex { x: x1 + radius, y: y1, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
            polyline.vertices.push(LwPolylineVertex { x: x2 - radius, y: y1, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
            polyline.vertices.push(LwPolylineVertex { x: x2, y: y1 + radius, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
            polyline.vertices.push(LwPolylineVertex { x: x2, y: y2 - radius, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
            polyline.vertices.push(LwPolylineVertex { x: x2 - radius, y: y2, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
            polyline.vertices.push(LwPolylineVertex { x: x1 + radius, y: y2, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
            polyline.vertices.push(LwPolylineVertex { x: x1, y: y2 - radius, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
            polyline.vertices.push(LwPolylineVertex { x: x1, y: y1 + radius, id: 0, starting_width: 0.0, ending_width: 0.0, bulge });
        } else {
            // Regular rectangle
            polyline.vertices.push(LwPolylineVertex { x: x1, y: y1, id: 0, starting_width: 0.0, ending_width: 0.0, bulge: 0.0 });
            polyline.vertices.push(LwPolylineVertex { x: x2, y: y1, id: 0, starting_width: 0.0, ending_width: 0.0, bulge: 0.0 });
            polyline.vertices.push(LwPolylineVertex { x: x2, y: y2, id: 0, starting_width: 0.0, ending_width: 0.0, bulge: 0.0 });
            polyline.vertices.push(LwPolylineVertex { x: x1, y: y2, id: 0, starting_width: 0.0, ending_width: 0.0, bulge: 0.0 });
        }

        // Set closed flag using the flags field
        polyline.flags |= 1; // Set closed flag

        // Create entity with common properties
        let mut entity = Entity::new(EntityType::LwPolyline(polyline));
        entity.common.layer = command.layer_name.clone();

        // Set color from layer
        if let Some(color) = self.layer_colors.get(&command.layer_name) {
            entity.common.color = color.clone();
        }

        self.drawing.add_entity(entity);
        Ok(())
    }

    fn add_circle(&mut self, command: &DrawCommand) -> DxfResult<()> {
        let mut circle = Circle::default();

        // Use Z1 coordinate if provided, otherwise use thickness or 0
        let z = command.z1.unwrap_or(command.thickness.unwrap_or(0.0));
        circle.center = Point::new(command.x1, command.y1, z);
        circle.radius = command.radius;

        // Set thickness for extrusion if provided (separate from Z coordinate)
        if let Some(thickness) = command.thickness {
            circle.thickness = thickness;
        }

        // Create entity with common properties
        let mut entity = Entity::new(EntityType::Circle(circle));
        entity.common.layer = command.layer_name.clone();

        // Set color from layer
        if let Some(color) = self.layer_colors.get(&command.layer_name) {
            entity.common.color = color.clone();
        }

        self.drawing.add_entity(entity);
        Ok(())
    }

    fn add_arc(&mut self, command: &DrawCommand) -> DxfResult<()> {
        let mut arc = Arc::default();

        // Use Z1 coordinate if provided, otherwise use thickness or 0
        let z = command.z1.unwrap_or(command.thickness.unwrap_or(0.0));
        arc.center = Point::new(command.x1, command.y1, z);
        arc.radius = command.radius;

        // Set angles
        if let (Some(start_angle), Some(end_angle)) = (command.start_angle, command.end_angle) {
            arc.start_angle = start_angle;
            arc.end_angle = end_angle;
        }

        // Set thickness for extrusion if provided (separate from Z coordinate)
        if let Some(thickness) = command.thickness {
            arc.thickness = thickness;
        }

        // Create entity with common properties
        let mut entity = Entity::new(EntityType::Arc(arc));
        entity.common.layer = command.layer_name.clone();

        // Set color from layer
        if let Some(color) = self.layer_colors.get(&command.layer_name) {
            entity.common.color = color.clone();
        }

        self.drawing.add_entity(entity);
        Ok(())
    }

    fn add_polyline(&mut self, command: &DrawCommand) -> DxfResult<()> {
        if let Some(points) = &command.points {
            // Check if we have 3D points (Z coordinates) - use 3D polyline if so
            let has_3d_points = points.iter().any(|p| p.len() >= 3 && p[2] != 0.0);

            if has_3d_points {
                // Use 3D polyline (Polyline entity) for 3D points
                let mut polyline_3d = Polyline::default();

                // Set thickness if provided
                if let Some(thickness) = command.thickness {
                    polyline_3d.thickness = thickness;
                }

                // Create entity with common properties
                let mut entity = Entity::new(EntityType::Polyline(polyline_3d));
                entity.common.layer = command.layer_name.clone();

                // Set color from layer
                if let Some(color) = self.layer_colors.get(&command.layer_name) {
                    entity.common.color = color.clone();
                }

                self.drawing.add_entity(entity);

                // Add vertices as separate entities
                for point in points {
                    if point.len() >= 3 {
                        let mut vertex = Vertex::default();
                        vertex.location = Point::new(point[0], point[1], point[2]);

                        let mut vertex_entity = Entity::new(EntityType::Vertex(vertex));
                        vertex_entity.common.layer = command.layer_name.clone();

                        if let Some(color) = self.layer_colors.get(&command.layer_name) {
                            vertex_entity.common.color = color.clone();
                        }

                        self.drawing.add_entity(vertex_entity);
                    }
                }

                // Add sequence end marker
                let seqend = Seqend::default();
                let seqend_entity = Entity::new(EntityType::Seqend(seqend));
                self.drawing.add_entity(seqend_entity);

            } else {
                // Use 2D lightweight polyline for 2D points
                let mut polyline = LwPolyline::default();

                // Set thickness if provided
                if let Some(thickness) = command.thickness {
                    polyline.thickness = thickness;
                }

                // Add vertices with bulge support
                for point in points {
                    if point.len() >= 2 {
                        let bulge = if point.len() >= 4 { point[3] } else { 0.0 };

                        polyline.vertices.push(LwPolylineVertex {
                            x: point[0],
                            y: point[1],
                            id: 0,
                            starting_width: 0.0,
                            ending_width: 0.0,
                            bulge
                        });
                    }
                }

                // Create entity with common properties
                let mut entity = Entity::new(EntityType::LwPolyline(polyline));
                entity.common.layer = command.layer_name.clone();

                // Set color from layer
                if let Some(color) = self.layer_colors.get(&command.layer_name) {
                    entity.common.color = color.clone();
                }

                self.drawing.add_entity(entity);
            }
        }
        Ok(())
    }

    fn add_text(&mut self, command: &DrawCommand) -> DxfResult<()> {
        if !command.text.is_empty() {
            let mut text = Text::default();

            // Use Z1 coordinate if provided, otherwise use thickness or 0
            let z = command.z1.unwrap_or(command.thickness.unwrap_or(0.0));
            text.location = Point::new(command.x1, command.y1, z);
            text.value = command.text.clone();
            text.text_height = command.size;

            // Create entity with common properties
            let mut entity = Entity::new(EntityType::Text(text));
            entity.common.layer = command.layer_name.clone();

            // Set color from layer
            if let Some(color) = self.layer_colors.get(&command.layer_name) {
                entity.common.color = color.clone();
            }

            self.drawing.add_entity(entity);
        }
        Ok(())
    }

    pub fn export_to_string(&self) -> DxfResult<String> {
        let mut buffer = Vec::new();
        self.drawing.save(&mut buffer)?;
        Ok(String::from_utf8_lossy(&buffer).to_string())
    }

    pub fn export_to_bytes(&self) -> DxfResult<Vec<u8>> {
        let mut buffer = Vec::new();
        self.drawing.save(&mut buffer)?;
        Ok(buffer)
    }
}

pub fn export_draw_commands_to_dxf(commands: &[DrawCommand]) -> Result<String, String> {
    export_draw_commands_to_dxf_with_options(commands, true)
}

pub fn export_draw_commands_to_dxf_with_options(commands: &[DrawCommand], join_shapes: bool) -> Result<String, String> {
    let processed_commands = if join_shapes {
        post_process_draw_commands(commands)
    } else {
        commands.to_vec()
    };

    let mut exporter = DxfExporter::new();

    exporter.add_draw_commands(&processed_commands)
        .map_err(|e| format!("Failed to add draw commands: {}", e))?;

    exporter.export_to_string()
        .map_err(|e| format!("Failed to export DXF: {}", e))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_post_processor_joins_connected_lines() {
        // Create three connected lines that should be joined into a polyline
        let commands = vec![
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 0.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 10.0, y1: 0.0, x2: 10.0, y2: 10.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 10.0, y1: 10.0, x2: 0.0, y2: 10.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should result in a single polyline command
        assert_eq!(processed.len(), 1);
        assert_eq!(processed[0].command_type, "polyline");

        if let Some(points) = &processed[0].points {
            assert_eq!(points.len(), 4); // 4 points for the connected path
            assert_eq!(points[0], vec![0.0, 0.0, 0.0, 0.0]);
            assert_eq!(points[1], vec![10.0, 0.0, 0.0, 0.0]);
            assert_eq!(points[2], vec![10.0, 10.0, 0.0, 0.0]);
            assert_eq!(points[3], vec![0.0, 10.0, 0.0, 0.0]);
        } else {
            panic!("Expected polyline to have points");
        }
    }

    #[test]
    fn test_3d_coordinate_handling() {
        // Test that 3D coordinates are properly handled in DXF export
        let commands = vec![
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 10.0,
                z1: Some(-5.0), z2: Some(-15.0), // 3D line with different Z coordinates
                radius: 0.0,
                color: "blue".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "3D_LAYER".to_string(),
                thickness: Some(-10.0), // Thickness for extrusion
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "polyline".to_string(),
                x1: 0.0, y1: 0.0, x2: 0.0, y2: 0.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "green".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "3D_LAYER".to_string(),
                thickness: Some(-8.0),
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None,
                points: Some(vec![
                    vec![20.0, 20.0, -5.0, 0.0],   // x, y, z, bulge
                    vec![30.0, 20.0, -10.0, 0.0],
                    vec![30.0, 30.0, -15.0, 0.0],
                    vec![20.0, 30.0, -10.0, 0.0],
                    vec![20.0, 20.0, -5.0, 0.0],
                ]),
            },
        ];

        // Test DXF export with 3D coordinates
        let result = export_draw_commands_to_dxf(&commands);
        assert!(result.is_ok(), "DXF export should succeed with 3D coordinates");

        let dxf_content = result.unwrap();

        // Verify that the DXF contains 3D coordinate information
        assert!(dxf_content.contains("3D_LAYER"), "DXF should contain the 3D layer");

        // The DXF should contain Z coordinate values (as strings in the DXF format)
        assert!(dxf_content.contains("-5") || dxf_content.contains("-10") || dxf_content.contains("-15"),
                "DXF should contain Z coordinate values");

        println!("3D DXF Export Test Passed!");
        println!("DXF Content Length: {} bytes", dxf_content.len());
    }

    #[test]
    fn test_post_processor_preserves_different_layers() {
        let commands = vec![
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 0.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "layer1".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 10.0, y1: 0.0, x2: 10.0, y2: 10.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "blue".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "layer2".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should result in two separate commands (different layers)
        assert_eq!(processed.len(), 2);

        // Check that both layers are present (order may vary due to HashMap)
        let layer_names: std::collections::HashSet<String> = processed.iter()
            .map(|cmd| cmd.layer_name.clone())
            .collect();
        assert!(layer_names.contains("layer1"));
        assert!(layer_names.contains("layer2"));
    }

    #[test]
    fn test_post_processor_preserves_non_joinable_shapes() {
        let commands = vec![
            DrawCommand {
                command_type: "circle".to_string(),
                x1: 5.0, y1: 5.0, x2: 0.0, y2: 0.0,
                z1: None, z2: None,
                radius: 3.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "rectangle".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 10.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "blue".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should preserve both commands as-is
        assert_eq!(processed.len(), 2);
        assert_eq!(processed[0].command_type, "circle");
        assert_eq!(processed[1].command_type, "rectangle");
    }

    #[test]
    fn test_rectangle_with_connected_lines() {
        // Test a rectangle with lines that connect to its corners
        let commands = vec![
            DrawCommand {
                command_type: "rectangle".to_string(),
                x1: 10.0, y1: 10.0, x2: 20.0, y2: 20.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 10.0, y1: 10.0, x2: 5.0, y2: 5.0, // Line from rectangle corner
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Rectangle should remain separate from the line (rectangles are not joinable)
        assert_eq!(processed.len(), 2);

        // Find rectangle and line in processed commands
        let rect_cmd = processed.iter().find(|cmd| cmd.command_type == "rectangle");
        let line_cmd = processed.iter().find(|cmd| cmd.command_type == "line");

        assert!(rect_cmd.is_some(), "Rectangle command should be preserved");
        assert!(line_cmd.is_some(), "Line command should be preserved");
    }

    #[test]
    fn test_polyline_joining_with_gaps() {
        // Test polylines that have small gaps (within tolerance)
        let commands = vec![
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 0.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 10.0000001, y1: 0.0000001, x2: 20.0, y2: 0.0, // Very small gap
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should join into a single polyline due to small gap within tolerance
        assert_eq!(processed.len(), 1);
        assert_eq!(processed[0].command_type, "polyline");

        if let Some(points) = &processed[0].points {
            assert_eq!(points.len(), 3); // 3 points for the joined line
        }
    }

    #[test]
    fn test_shape_ordering_and_arrangement() {
        // Test that shapes are properly ordered when joining
        // Create a path: A->B, C->D, B->C (should be reordered to A->B->C->D)
        let commands = vec![
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 0.0, // A->B
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 20.0, y1: 0.0, x2: 30.0, y2: 0.0, // C->D
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 10.0, y1: 0.0, x2: 20.0, y2: 0.0, // B->C (connects the other two)
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should result in a single polyline
        assert_eq!(processed.len(), 1);
        assert_eq!(processed[0].command_type, "polyline");

        if let Some(points) = &processed[0].points {
            // Should have 4 points in the correct order: A, B, C, D
            assert_eq!(points.len(), 4);
            assert_eq!(points[0], vec![0.0, 0.0, 0.0, 0.0]);   // A
            assert_eq!(points[1], vec![10.0, 0.0, 0.0, 0.0]);  // B
            assert_eq!(points[2], vec![20.0, 0.0, 0.0, 0.0]);  // C
            assert_eq!(points[3], vec![30.0, 0.0, 0.0, 0.0]);  // D
        } else {
            panic!("Expected polyline to have points");
        }
    }

    #[test]
    fn test_reverse_shape_connection() {
        // Test that shapes are reversed when necessary to maintain continuity
        // Create: A->B, D->C where B connects to D (should reverse second line to C->D)
        let commands = vec![
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 0.0, // A->B
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 20.0, y1: 0.0, x2: 10.0, y2: 0.0, // D->C (should be reversed to C->D)
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should result in a single polyline
        assert_eq!(processed.len(), 1);
        assert_eq!(processed[0].command_type, "polyline");

        if let Some(points) = &processed[0].points {
            // Should have 3 points: A, B, D (with the second line reversed)
            assert_eq!(points.len(), 3);
            assert_eq!(points[0], vec![0.0, 0.0, 0.0, 0.0]);   // A
            assert_eq!(points[1], vec![10.0, 0.0, 0.0, 0.0]);  // B
            assert_eq!(points[2], vec![20.0, 0.0, 0.0, 0.0]);  // D (from reversed D->C line)
        } else {
            panic!("Expected polyline to have points");
        }
    }

    #[test]
    fn test_circles_and_arcs_preserved() {
        // Test that circles and arcs are not joined and preserve their geometry
        let commands = vec![
            DrawCommand {
                command_type: "circle".to_string(),
                x1: 5.0, y1: 5.0, x2: 0.0, y2: 0.0,
                z1: None, z2: None,
                radius: 3.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "arc".to_string(),
                x1: 10.0, y1: 10.0, x2: 0.0, y2: 0.0,
                z1: None, z2: None,
                radius: 5.0,
                color: "blue".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: Some(0.0), end_angle: Some(90.0), clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 10.0, y2: 0.0,
                z1: None, z2: None,
                radius: 0.0,
                color: "green".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should preserve all three commands as separate entities
        assert_eq!(processed.len(), 3);

        // Find each command type
        let circle_cmd = processed.iter().find(|cmd| cmd.command_type == "circle");
        let arc_cmd = processed.iter().find(|cmd| cmd.command_type == "arc");
        let line_cmd = processed.iter().find(|cmd| cmd.command_type == "line");

        assert!(circle_cmd.is_some(), "Circle should be preserved");
        assert!(arc_cmd.is_some(), "Arc should be preserved");
        assert!(line_cmd.is_some(), "Line should be preserved");

        // Verify circle properties are preserved
        let circle = circle_cmd.unwrap();
        assert_eq!(circle.radius, 3.0);
        assert_eq!(circle.x1, 5.0);
        assert_eq!(circle.y1, 5.0);

        // Verify arc properties are preserved
        let arc = arc_cmd.unwrap();
        assert_eq!(arc.radius, 5.0);
        assert_eq!(arc.x1, 10.0);
        assert_eq!(arc.y1, 10.0);
        assert_eq!(arc.start_angle, Some(0.0));
        assert_eq!(arc.end_angle, Some(90.0));
    }

    #[test]
    fn test_arc_to_line_joining_with_bulge() {
        // Test that arcs can be joined to lines while preserving curvature using bulge values
        let commands = vec![
            DrawCommand {
                command_type: "line".to_string(),
                x1: 0.0, y1: 0.0, x2: 15.0, y2: 0.0, // Horizontal line to (15, 0)
                z1: None, z2: None,
                radius: 0.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "arc".to_string(),
                x1: 10.0, y1: 0.0, x2: 0.0, y2: 0.0, // Arc centered at (10, 0) with radius 5
                z1: None, z2: None,
                radius: 5.0,
                color: "red".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: Some(0.0), end_angle: Some(90.0), // 90-degree arc from (15,0) to (10,5)
                clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should result in a single polyline that combines line and arc
        assert_eq!(processed.len(), 1);
        assert_eq!(processed[0].command_type, "polyline");

        if let Some(points) = &processed[0].points {
            // Debug: print actual points to understand the structure
            println!("Actual points: {:?}", points);

            // Should have at least 3 points for line + arc
            assert!(points.len() >= 3, "Expected at least 3 points, got {}", points.len());

            // First point: line start
            assert_eq!(points[0][0], 0.0);
            assert_eq!(points[0][1], 0.0);

            // Check that we have a bulge value somewhere (indicating arc preservation)
            let has_bulge = points.iter().any(|p| p.len() >= 4 && p[3].abs() > 0.001);
            assert!(has_bulge, "Should have bulge values to represent the arc");

            // The last point should be the arc end
            let last_point = &points[points.len() - 1];
            assert_eq!(last_point[0], 10.0); // 10 + 5*cos(90°) = 10 + 0
            assert_eq!(last_point[1], 5.0);  // 0 + 5*sin(90°) = 0 + 5
        } else {
            panic!("Expected polyline to have points");
        }
    }

    #[test]
    fn test_line_to_arc_joining() {
        // Test joining in the opposite direction: line connects to arc start
        let commands = vec![
            DrawCommand {
                command_type: "arc".to_string(),
                x1: 5.0, y1: 5.0, x2: 0.0, y2: 0.0, // Arc centered at (5, 5) with radius 5
                z1: None, z2: None,
                radius: 5.0,
                color: "blue".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: Some(180.0), end_angle: Some(270.0), // 90-degree arc from left to bottom
                clockwise: None,
                svg_path: None, points: None,
            },
            DrawCommand {
                command_type: "line".to_string(),
                x1: 5.0, y1: 0.0, x2: 15.0, y2: 0.0, // Line continuing from arc end
                z1: None, z2: None,
                radius: 0.0,
                color: "blue".to_string(),
                size: 1.0,
                text: String::new(),
                layer_name: "test_layer".to_string(),
                thickness: None,
                start_angle: None, end_angle: None, clockwise: None,
                svg_path: None, points: None,
            },
        ];

        let processed = post_process_draw_commands(&commands);

        // Should result in a single polyline
        assert_eq!(processed.len(), 1);
        assert_eq!(processed[0].command_type, "polyline");

        if let Some(points) = &processed[0].points {
            // Should have points representing the arc followed by the line
            assert!(points.len() >= 3, "Should have at least 3 points for arc + line");

            // Check that we have a bulge value somewhere (indicating arc preservation)
            let has_bulge = points.iter().any(|p| p.len() >= 4 && p[3].abs() > 0.001);
            assert!(has_bulge, "Should have bulge values to represent the arc");
        } else {
            panic!("Expected polyline to have points");
        }
    }
}
