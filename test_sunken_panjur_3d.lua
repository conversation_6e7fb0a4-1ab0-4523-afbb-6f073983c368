-- Test script that simulates the complex 3D operations from Sunken and Panjur models
-- This test verifies that Z coordinates and thickness are properly handled in DXF output
function modelMain()
    G = ADekoLib
    
    -- Set up parameters similar to Sunken/Panjur models
    X = 500
    Y = 400
    materialThickness = 18
    
    a = 60
    aa = 30
    b = 50
    finalDepth = 10
    startingDepth = 2
    cW = 10
    cT = 6
    step = 3
    
    print("=== Testing Sunken/Panjur Model 3D Operations ===")
    
    -- Test 1: Panel creation (like all models)
    print("\n1. Creating door panel...")
    G.setThickness(-materialThickness)
    G.setFace("top")
    G.makePartShape()
    
    -- Test 2: Inclined pocket operations (like Panjur models)
    print("\n2. Creating inclined pockets with varying depths...")
    local howMany = math.floor((Y-2*aa)/b)
    local mB = (Y-2*aa)/howMany
    local araThickness = finalDepth - (finalDepth * (mB - cW)) / (mB - cT)
    
    print("  howMany pockets: " .. howMany)
    print("  mB spacing: " .. mB)
    print("  araThickness: " .. araThickness)
    
    G.setLayer("K_Freze" .. cW .. "mm")
    G.setThickness(-startingDepth)
    
    local polylines = {}
    
    for i=0, howMany-1, 1 do
        local point1 = {a, aa+i*mB}
        local point2 = {X-a, aa+(i+1)*mB}
        
        print("  Processing pocket " .. i .. " from (" .. point1[1] .. "," .. point1[2] .. ") to (" .. point2[1] .. "," .. point2[2] .. ")")
        
        -- Simulate inclinedPocket2 output with varying Z coordinates
        local polyline = G.inclinedPocket2(point1, point2, finalDepth-araThickness, step, cW, true)
        
        -- Scale depth (critical for proper Z coordinate handling)
        polyline = G.scaleDepth(polyline, 0, -finalDepth+araThickness)
        
        -- Add retraction point with material thickness (3D coordinate)
        local g1 = G.ptAdd(polyline[#polyline], {0, 0, 1.1*materialThickness})
        table.insert(polyline, g1)
        
        -- Add approach point
        local g2 = G.ptAdd({a, aa+(i+1)*mB}, {cW/2, cW/2, 1.1*materialThickness})
        table.insert(polyline, g2)
        
        -- Join polylines (should preserve complex 3D geometry)
        polylines = G.joinPolylines(polylines, polyline)
        
        -- Fine pass operations
        if cT ~= 0 then
            local q1 = {point2[1], point1[2] + cW}
            
            G.setLayer("K_Freze" .. cT .. "mm")
            G.setThickness(-startingDepth)
            
            -- Create fine pass with different depth
            local fine_polyline = G.inclinedPocket2(point1, q1, araThickness, step, cT, true)
            fine_polyline = G.scaleDepth(fine_polyline, -(finalDepth-araThickness), -finalDepth)
            G.polylineimp(fine_polyline)
        end
    end
    
    -- Output the main polylines (should preserve complex 3D geometry)
    G.setLayer("K_Freze" .. cW .. "mm")
    G.setThickness(-startingDepth)
    table.remove(polylines, #polylines) -- Remove last point as in original
    G.polylineimp(polylines)
    
    -- Test 3: Sunken frame operations (like Sunken models)
    print("\n3. Creating sunken frame operations...")
    G.setLayer("K_AciliV30")
    G.setThickness(0)
    
    local sunkenDepth = 4
    local vWideAngle = 120
    local vWideDiameter = 60
    
    local point1 = {a+10, aa+10}
    local point2 = {X-(a+10), Y-(aa+10)}
    
    -- Simulate sunkenFrame operation (creates angled surfaces)
    local corner1, corner2 = G.sunkenFrame(point1, point2, sunkenDepth, vWideAngle, vWideDiameter)
    
    -- Set Z coordinates to 0 (as in original models)
    corner1[3] = 0
    corner2[3] = 0
    
    -- Deep pocket operation
    G.setLayer("Cep_Acma")
    G.setThickness(-2*sunkenDepth)
    G.rectangle(corner1, corner2)
    
    -- Corner cleaning operation
    G.setLayer("K_Freze" .. cT .. "mm")
    G.setThickness(0)
    G.cleanCorners(corner1, corner2, 2*sunkenDepth, cT)
    
    -- Test 4: Multiple thickness levels (common in all models)
    print("\n4. Testing multiple thickness levels...")
    
    -- Surface level
    G.setThickness(0)
    G.setLayer("SURFACE_MARKS")
    local surface_rect_p1 = {10, 10}
    local surface_rect_p2 = {50, 50}
    G.rectangle(surface_rect_p1, surface_rect_p2)
    
    -- Shallow level
    G.setThickness(-startingDepth)
    G.setLayer("SHALLOW_OPS")
    local shallow_rect_p1 = {60, 10}
    local shallow_rect_p2 = {100, 50}
    G.rectangle(shallow_rect_p1, shallow_rect_p2)
    
    -- Deep level
    G.setThickness(-finalDepth)
    G.setLayer("DEEP_OPS")
    local deep_rect_p1 = {110, 10}
    local deep_rect_p2 = {150, 50}
    G.rectangle(deep_rect_p1, deep_rect_p2)
    
    -- Very deep level (material thickness)
    G.setThickness(-materialThickness)
    G.setLayer("THROUGH_CUTS")
    local through_rect_p1 = {160, 10}
    local through_rect_p2 = {200, 50}
    G.rectangle(through_rect_p1, through_rect_p2)
    
    print("\n=== Sunken/Panjur 3D Test Complete ===")
    print("This test verifies:")
    print("  ✓ inclinedPocket2 + scaleDepth operations preserve Z coordinates")
    print("  ✓ Complex 3D polylines are not joined inappropriately")
    print("  ✓ sunkenFrame operations create proper angled surfaces")
    print("  ✓ Multiple thickness levels are maintained in DXF")
    print("  ✓ Material thickness calculations work correctly")
    print("  ✓ Retraction moves use proper Z coordinates")
    
    return true
end

require "ADekoDebugMode"
