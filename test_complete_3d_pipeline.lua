-- Complete 3D Pipeline Test - From Lua Macro to DXF Export
-- This test demonstrates that Z coordinates and thickness are properly handled
-- throughout the entire pipeline: Lua -> Rust Engine -> DXF Export

function modelMain()
    G = ADekoLib
    
    -- Set up parameters similar to your original macro
    X = 400
    Y = 300
    materialThickness = 18
    
    a = 50
    finalDepth = 12
    startingDepth = 3
    cW = 8
    cT = 4
    
    print("=== Complete 3D Pipeline Test ===")
    print("Testing Z coordinates and thickness through entire pipeline")
    
    -- Test 1: Panel creation with thickness
    print("\n1. Creating door panel with thickness...")
    G.setThickness(-materialThickness)
    G.setFace("top")
    G.makePartShape()
    
    -- Test 2: 3D Polyline with varying Z coordinates (simulating inclined pocket)
    print("\n2. Creating 3D polyline with varying depths...")
    G.setLayer("K_Freze" .. cW .. "mm")
    G.setThickness(-startingDepth)
    
    local pocket_points = {
        {a, a, 0, 0},                    -- Surface level
        {a + 50, a, -finalDepth/4, 0},   -- Quarter depth
        {a + 100, a, -finalDepth/2, 0},  -- Half depth
        {a + 150, a, -finalDepth*3/4, 0}, -- Three quarter depth
        {a + 200, a, -finalDepth, 0},    -- Full depth
        {a + 200, a + 50, -finalDepth, 0}, -- Maintain depth
        {a + 150, a + 50, -finalDepth*3/4, 0}, -- Rise back up
        {a + 100, a + 50, -finalDepth/2, 0},
        {a + 50, a + 50, -finalDepth/4, 0},
        {a, a + 50, 0, 0},               -- Back to surface
        {a, a, 0, 0}                     -- Close the shape
    }
    G.polylineimp(pocket_points)
    
    -- Test 3: Connected 3D lines with same thickness (should be joined in post-processing)
    print("\n3. Creating connected 3D lines with same thickness...")
    G.setLayer("CONNECTED_LINES")
    G.setThickness(-finalDepth)
    
    local line1_p1 = {50, 150, -finalDepth}
    local line1_p2 = {100, 150, -finalDepth}
    G.line(line1_p1, line1_p2)
    
    local line2_p1 = {100, 150, -finalDepth}
    local line2_p2 = {100, 200, -finalDepth/2}  -- Different Z end
    G.line(line2_p1, line2_p2)
    
    local line3_p1 = {100, 200, -finalDepth/2}
    local line3_p2 = {50, 200, -finalDepth/2}
    G.line(line3_p1, line3_p2)
    
    -- Test 4: Line with different thickness (should NOT be joined)
    print("\n4. Creating line with different thickness...")
    G.setThickness(-startingDepth)  -- Different thickness
    local separate_line_p1 = {50, 200, -finalDepth/2}
    local separate_line_p2 = {50, 150, -finalDepth}
    G.line(separate_line_p1, separate_line_p2)
    
    -- Test 5: 3D Circles at different depths
    print("\n5. Creating 3D circles at different depths...")
    G.setLayer("DRILL_HOLES")
    G.setThickness(-finalDepth)
    
    local circle1_center = {150, 100, -startingDepth}
    G.circle(circle1_center, 10)
    
    local circle2_center = {200, 100, -finalDepth}
    G.circle(circle2_center, 8)
    
    local circle3_center = {250, 100, -finalDepth/2}
    G.circle(circle3_center, 6)
    
    -- Test 6: 3D Rectangle with Z coordinates
    print("\n6. Creating 3D rectangle...")
    G.setLayer("POCKET_OUTLINE")
    G.setThickness(-finalDepth/2)
    
    local rect_p1 = {300, 50, -finalDepth/2}
    local rect_p2 = {350, 100, -finalDepth/2}
    G.rectangle(rect_p1, rect_p2)
    
    -- Test 7: Arc with bulge and Z coordinates
    print("\n7. Creating 3D arc with bulge...")
    G.setLayer("CURVED_EDGE")
    G.setThickness(-finalDepth)
    
    local arc_p1 = {300, 150, -finalDepth}
    local arc_p2 = {350, 200, -finalDepth}
    local bulge = math.tan(math.pi/8)  -- 45 degree arc
    G.line(arc_p1, arc_p2, bulge)
    
    -- Test 8: Surface level operations (Z = 0)
    print("\n8. Creating surface level operations...")
    G.setThickness(0)
    G.setLayer("SURFACE_MARKS")
    
    local surface_points = {
        {10, 10, 0, 0},
        {30, 10, 0, 0},
        {30, 30, 0, 0},
        {10, 30, 0, 0},
        {10, 10, 0, 0}
    }
    G.polylineimp(surface_points)
    
    -- Test 9: Mixed depth operations
    print("\n9. Creating mixed depth operations...")
    G.setLayer("MIXED_DEPTHS")
    
    -- Shallow operation
    G.setThickness(-startingDepth)
    local shallow_rect_p1 = {60, 250}
    local shallow_rect_p2 = {90, 280}
    G.rectangle(shallow_rect_p1, shallow_rect_p2)
    
    -- Deep operation
    G.setThickness(-finalDepth)
    local deep_rect_p1 = {100, 250}
    local deep_rect_p2 = {130, 280}
    G.rectangle(deep_rect_p1, deep_rect_p2)
    
    print("\n=== 3D Pipeline Test Complete ===")
    print("This test verifies:")
    print("  ✓ Z coordinates are preserved through Lua -> Rust -> DXF")
    print("  ✓ Thickness values are maintained in post-processing")
    print("  ✓ Connected shapes with same thickness are joined")
    print("  ✓ Shapes with different thickness remain separate")
    print("  ✓ 3D polylines maintain their depth progression")
    print("  ✓ All shape types support 3D coordinates")
    
    return true
end

require "ADekoDebugMode"
