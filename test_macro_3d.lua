-- Test script that simulates the 3D coordinate usage from your macro
function modelMain()
    G = ADekoLib
    
    -- Set up parameters similar to your macro
    X = 500
    Y = 400
    materialThickness = 18
    
    minimum = 200
    limit = 350
    a = 60
    aa = 30
    b = 50
    cW = 10
    cT = 6
    step = 3
    startingDepth = 2
    finalDepth = 10
    
    print("=== Testing Macro-style 3D Coordinates ===")
    
    -- Set up the door panel (similar to your macro)
    G.setThickness(-materialThickness)
    G.setFace("top")
    G.makePart<PERSON>hape()
    
    -- Test inclined pocket simulation with 3D coordinates
    local howMany = math.floor((Y-2*aa)/b)
    local mB = (Y-2*aa)/howMany
    local araThickness = finalDepth - (finalDepth * (mB - cW)) / (mB - cT)
    
    print("Calculated values:")
    print("  howMany: " .. howMany)
    print("  mB: " .. mB)
    print("  araThickness: " .. araThickness)
    
    -- Simulate the polyline creation from your macro
    local polylines = {}
    
    for i=0, howMany-1, 1 do
        local point1 = {a, aa+i*mB}
        local point2 = {X-a, aa+(i+1)*mB}
        
        print("Processing pocket " .. i .. " from (" .. point1[1] .. "," .. point1[2] .. ") to (" .. point2[1] .. "," .. point2[2] .. ")")
        
        -- Simulate inclined pocket points with 3D coordinates
        local pocket_points = {}
        local steps = 5  -- Simplified for testing
        
        for j=0, steps-1 do
            local x = point1[1] + (point2[1] - point1[1]) * j / steps
            local y = point1[2] + (point2[2] - point1[2]) * j / steps
            local z = -(j * finalDepth / steps)  -- Simulate depth progression
            local bulge = 0
            
            table.insert(pocket_points, {x, y, z, bulge})
        end
        
        -- Add final point
        table.insert(pocket_points, {point2[1], point2[2], -finalDepth, 0})
        
        -- Add retraction point (similar to your macro)
        local retraction_z = 1.1 * materialThickness
        table.insert(pocket_points, {point2[1], point2[2], retraction_z, 0})
        
        -- Test the polylineimp function with 3D coordinates
        G.setLayer("K_Freze" .. cW .. "mm")
        G.setThickness(-startingDepth)
        G.polylineimp(pocket_points)
        
        -- Test individual 3D operations
        if i == 0 then
            -- Test 3D line
            local line_p1 = {point1[1], point1[2], -startingDepth}
            local line_p2 = {point2[1], point2[2], -finalDepth}
            G.line(line_p1, line_p2)
            
            -- Test 3D rectangle
            local rect_p1 = {point1[1] - 5, point1[2] - 5, -finalDepth}
            local rect_p2 = {point1[1] + 5, point1[2] + 5, -finalDepth}
            G.rectangle(rect_p1, rect_p2)
            
            -- Test 3D circle
            local circle_center = {point1[1], point1[2], -finalDepth/2}
            G.circle(circle_center, cT/2)
        end
    end
    
    -- Test thickness variations (similar to your macro)
    G.setThickness(-finalDepth)
    local final_rect_p1 = {a, aa}
    local final_rect_p2 = {X-a, Y-aa}
    G.rectangle(final_rect_p1, final_rect_p2)
    
    -- Test surface level operations
    G.setThickness(0)
    local surface_points = {
        {a+cW/2, aa+cW/2, 0, 0},
        {X-(a+cW/2), aa+cW/2, 0, 0},
        {X-(a+cW/2), Y-(aa+cW/2), 0, 0},
        {a+cW/2, Y-(aa+cW/2), 0, 0},
        {a+cW/2, aa+cW/2, 0, 0}
    }
    G.polylineimp(surface_points)
    
    print("=== Macro-style 3D Test Complete ===")
    return true
end

require "ADekoDebugMode"
