-- Simple test to verify engine setup works
print("=== Engine Setup Test ===")

-- Set up the engine properly
local engine = require("makerjs_engine")
print("✓ makerjs_engine loaded")

ADekoLib.engine = engine
print("✓ ADekoLib.engine set")

function modelMain()
  G = ADekoLib
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end

  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  print("✓ Basic ADekoLib setup complete")
  
  -- Test simple arc creation
  print("Testing simple arc creation...")
  G.setLayer("TEST_simple")
  G.setThickness(-5)
  
  -- This should work now
  G.arc({100, 100}, 50, 0, 90, false, "test_arc")
  print("✓ Arc created successfully!")
  
  return true
end

require "ADekoDebugMode"
