-- Simple test script for the new makerjs_engine
print("=== Testing New MakerJS Engine ===")

-- Load the new engine
local engine = require('makerjs_engine')
print("✓ Engine loaded successfully")

-- Create a simple test model
engine.model_def("simple_test", function()

    -- Test basic shapes
    engine.layer("PANEL")
    engine.rect("frame", 0, 0, 200, 150)

    engine.layer("20MM")
    engine.circle("hole1", 50, 50, 10)
    engine.circle("hole2", 150, 50, 10)

    engine.layer("lines")
    engine.line("diagonal", 0, 0, 200, 150)

end)

print("✓ Model created successfully")

-- Export the model to JSON
local json_output = engine.export_model("simple_test")

-- Print the JSON (this will be captured by the enhanced print function)
print("Generated MakerJS JSON:")
print(json_output)

print("=== Test Complete ===")
print("The JSON output should be captured and visualized in the 2D/3D panels")
