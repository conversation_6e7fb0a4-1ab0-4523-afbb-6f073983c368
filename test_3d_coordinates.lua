-- Test script to verify 3D coordinate handling in the Rust Lua engine
function modelMain()
    G = ADekoLib
    
    -- Set up basic parameters
    G.setFace("top")
    G.setThickness(-10)  -- 10mm deep cut
    G.setLayer("TEST_3D")
    
    print("=== Testing 3D Coordinate Support ===")
    
    -- Test 1: 3D Line
    print("Test 1: 3D Line")
    local p1 = {10, 10, -5}  -- Point with Z coordinate
    local p2 = {50, 50, -15} -- Point with different Z coordinate
    G.line(p1, p2)
    
    -- Test 2: 3D Circle
    print("Test 2: 3D Circle")
    local center = {100, 100, -8}  -- Circle center with Z coordinate
    G.circle(center, 20)
    
    -- Test 3: 3D Rectangle
    print("Test 3: 3D Rectangle")
    local rect_p1 = {150, 150, -12}
    local rect_p2 = {200, 200, -12}
    G.rectangle(rect_p1, rect_p2)
    
    -- Test 4: 3D Polyline with varying Z coordinates
    print("Test 4: 3D Polyline")
    local polyline_points = {
        {250, 250, -5, 0},   -- x, y, z, bulge
        {300, 250, -10, 0},
        {300, 300, -15, 0},
        {250, 300, -10, 0},
        {250, 250, -5, 0}    -- Close the polyline
    }
    G.polylineimp(polyline_points)
    
    -- Test 5: Arc with bulge and Z coordinates
    print("Test 5: Arc with bulge")
    local arc_p1 = {350, 350, -8}
    local arc_p2 = {400, 400, -8}
    local bulge = math.tan(math.pi/8)  -- 45 degree arc
    G.line(arc_p1, arc_p2, bulge)
    
    print("=== 3D Coordinate Tests Complete ===")
    return true
end

require "ADekoDebugMode"
