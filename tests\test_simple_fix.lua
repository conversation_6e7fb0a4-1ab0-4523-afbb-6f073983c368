-- Simple test to verify the model_def fix
print("=== Testing Simple Fix ===")

-- Set up basic variables
X = 200
Y = 150
materialThickness = 18
modelParameters = ""

-- Define a simple modelMain function
function modelMain()
    print("modelMain() called successfully")
    
    G = ADekoLib
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()
    
    print("Basic ADekoLib functions completed")
    return true
end

-- Load ADekoDebugMode which should now work
print("Loading ADekoDebugMode...")
require "ADekoDebugMode"

print("=== Test Complete ===")
