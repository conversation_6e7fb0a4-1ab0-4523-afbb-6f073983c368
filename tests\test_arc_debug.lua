-- Simple test to debug arc bulge issues
X = 400  -- width
Y = 300  -- height
materialThickness = 18
G = require("ADekoLib")
local engine = require("makerjs_engine")

-- Initialize the debug model (for face layout)
engine.model_def("debug_model", function()
    engine.layer("debug")
end)

-- Initialize ADekoLib with makerjs_engine support
G.engine = engine
print("DEBUG: Set G.engine to", tostring(G.engine))
print("DEBUG: ADekoLib.engine is", tostring(ADekoLib.engine))
print("DEBUG: G and ADekoLib are the same?", G == ADekoLib)

-- Create a temporary model for the actual geometry from modelMain()
engine.model_def("temp_model", function()
    -- This model will be populated by ADekoLib calls during modelMain()
end)

-- Set the engine to use the temp_model for geometry
engine.set_current_model("temp_model")

-- Enable debug output for arcs
G.debug_arcs = true

function modelMain()
  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  print("=== Simple Arc Debug Test ===")
  print("Shape counter initial value:", G.shape_counter)
  
  -- Test 1: Direct line call with bulge
  print("Test 1: Direct line call with bulge")
  G.setLayer("TEST_line_arc")
  G.setThickness(-5)
  
  print("Calling G.line with bulge...")
  G.line({50, 50}, {150, 50}, 0.5)  -- Positive bulge
  
  print("Shape counter after line call:", G.shape_counter)
  
  print("=== Debug test completed ===")
end

-- Set up global variables that ADekoLib expects
_G.showPoints = true
G.start()
G.showPoints(true)
G.enableListing(true)
modelMain()
G.finish()

-- Export the temporary model that contains the actual geometry from modelMain()
local temp_json = engine.export_model("temp_model")
print("Geometry model JSON generated:")
print(temp_json)
