-- Test to verify layer names are correctly passed from AdekoLib to visualization

-- Global variables
X = 300
Y = 200
materialThickness = 18

function modelMain()
    G = AdekoLib

    print("=== Testing Layer Name Propagation ===")

    -- Test 1: PANEL layer (should appear as "PANEL")
    print("Creating PANEL layer...")
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This should create PANEL layer

    -- Test 2: Custom layer names
    print("Creating K_Freze10mm layer...")
    G.setLayer("K_Freze10mm")
    G.setThickness(-3)
    G.rectangle({50, 50}, {250, 100})

    print("Creating K_AciliV90 layer...")
    G.setLayer("K_AciliV90")
    G.setThickness(-2)
    G.rectangle({50, 120}, {250, 170})

    print("Creating 8MM layer...")
    <PERSON><PERSON>setLayer("8MM")
    G.setThickness(-2)
    <PERSON><PERSON>circle({150, 150}, 20)

    print("=== Layer Test Complete ===")
    print("Expected layers in visualization:")
    print("- PANEL (door panel)")
    print("- K_Freze10mm (cutting operation)")
    print("- K_AciliV90 (V-bit operation)")
    print("- 8MM (numeric tool layer)")

    return true
end

require "ADekoDebugMode"
