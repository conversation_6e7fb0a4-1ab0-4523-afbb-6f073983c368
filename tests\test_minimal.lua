-- Minimal test to check AdekoLib availability

-- Global variables
X = 200
Y = 150
materialThickness = 18

function modelMain()
    print("modelMain() started")
    
    -- Check if AdekoLib exists
    if AdekoLib then
        print("✓ AdekoLib is available")
        G = AdekoLib
        print("✓ G assigned successfully")
        
        -- Try basic operations
        G.setFace("top")
        print("✓ setFace called")
        
        G.setThickness(-materialThickness)
        print("✓ setThickness called")
        
        G.makePartShape()
        print("✓ makePartShape called")
        
        -- Add a simple shape
        G.setLayer("TEST")
        G.rectangle({50, 50}, {150, 100})
        print("✓ Rectangle added")
        
    else
        print("✗ AdekoLib is not available")
        print("Available globals:")
        for k, v in pairs(_G) do
            if type(v) == "table" and k ~= "_G" then
                print("  " .. k .. " (table)")
            end
        end
    end
    
    print("modelMain() completed")
    return true
end

require "ADekoDebugMode"
