-- Test script for enhanced door model color visualization
-- This script demonstrates the new color scheme and layer categorization

-- Set up door parameters (must be global for AdekoDebugMode)
X = 600  -- Door width
Y = 800  -- Door height
materialThickness = 18

function modelMain()
    -- AdekoLib is already initialized by AdekoDebugMode, just assign to G
    G = AdekoLib

    -- Create door panel (PANEL layer - should appear as filled brown rectangle)
    G.setFace("top")
    G.setThickness(-materialThickness)
    G.makePartShape()  -- This creates the PANEL layer automatically

    -- TOP SURFACE OPERATIONS
    G.setFace("top")

    -- 1. Structural elements (orange)
    G.setLayer("LMM_Frame")
    G.setThickness(-2)
    G.rectangle({50, 50}, {550, 100})

    -- 2. Cutting operations (red spectrum)
    G.setLayer("H_Freze20mm")  -- Orange red for top face cuts
    G.setThickness(-5)
    G.rectangle({100, 200}, {500, 250})

    G.setLayer("K_Freze10mm")  -- <PERSON><PERSON> for top face grooves
    G.setThickness(-3)
    G.circle({300, 400}, 50)

    -- 3. V-bit operations (purple spectrum)
    G.setLayer("K_AciliV90")  -- Dark orchid for V-bit operations
    G.setThickness(-2)
    G.rectangle({150, 500}, {450, 550})

    G.setLayer("K_AciliV_Pah")  -- Blue violet for V-bit chamfers
    G.setThickness(-1)
    G.rectangle({0, 0}, {X, Y})  -- Edge chamfer

    -- 4. Ball nose operations (blue spectrum)
    G.setLayer("K_Ballnose6mm")  -- Royal blue for ball nose
    G.setThickness(-4)
    G.circle({150, 650}, 30)
    G.circle({450, 650}, 30)

    G.setLayer("K_Desen")  -- Dodger blue for decorative patterns
    G.setThickness(-2)
    G.rectangle({200, 600}, {400, 700})

    -- 5. Radial operations (green spectrum)
    G.setLayer("H_Raduslu_Pah_DIS")  -- Forest green for radial chamfers
    G.setThickness(-1.5)
    G.circle({300, 300}, 80)

    -- 6. Special operations (yellow/orange spectrum)
    G.setLayer("CLEANCORNERS")  -- Orange for corner cleanup
    G.setThickness(-1)
    G.rectangle({0, 0}, {50, 50})
    G.rectangle({X-50, 0}, {X, 50})
    G.rectangle({0, Y-50}, {50, Y})
    G.rectangle({X-50, Y-50}, {X, Y})

    -- 7. Numeric tool layers (HSL color generation)
    G.setLayer("5MM")  -- Should generate HSL color based on diameter
    G.setThickness(-2)
    G.circle({100, 100}, 15)

    G.setLayer("12MM")  -- Different HSL color
    G.setThickness(-3)
    G.circle({500, 100}, 20)

    -- BOTTOM SURFACE OPERATIONS (should appear semi-transparent)
    G.setFace("bottom")

    -- Bottom face operations with _SF suffix
    G.setLayer("H_Freze20mm_SF")  -- Crimson for bottom face cuts
    G.setThickness(-5)
    G.rectangle({100, 300}, {500, 350})

    G.setLayer("K_Freze10mm_SF")  -- Fire brick for bottom face grooves
    G.setThickness(-3)
    G.rectangle({200, 400}, {400, 450})

    -- Hardware mounting pockets
    G.setLayer("POCKET_Hardware")
    G.setThickness(-8)
    -- Hinge pockets
    G.rectangle({20, 100}, {80, 180})  -- Top hinge
    G.rectangle({20, 360}, {80, 440})  -- Middle hinge
    G.rectangle({20, 620}, {80, 700})  -- Bottom hinge

    print("✓ Enhanced door model visualization test completed")
    print("✓ PANEL layer: Filled brown rectangle (door panel)")
    print("✓ Top face operations: Fully opaque with enhanced colors")
    print("✓ Bottom face operations (_SF): Semi-transparent")
    print("✓ Layer categories: Structural, Cutting, V-bit, Ball nose, Radial, Special")
    print("✓ Numeric layers: HSL color generation based on tool diameter")

    return true
end

require "ADekoDebugMode"
