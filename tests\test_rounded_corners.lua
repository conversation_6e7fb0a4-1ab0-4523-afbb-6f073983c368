-- Test script to verify rounded corner issue in makePartShape
-- This will help identify the makePartShape bulge issue

function modelMain()
  G = ADekoLib
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end

  G.setThickness(-materialThickness)
  G.setFace("top")
  
  -- Enable arc debugging
  G.debug_arcs = true
  
  print("=== Rounded Corners Test ===")
  
  -- Test 1: Simple rectangle (should work fine)
  print("Test 1: Simple rectangle")
  G.makePartShape()  -- Default rectangle
  
  -- Test 2: Rectangle with rounded corners (this is where the issue occurs)
  print("Test 2: Rectangle with rounded corners")
  
  local edgeCornerRExist = 3
  local bulge = math.tan(math.pi/8)  -- This creates the rounded corners
  
  -- This is what your original script does - and it causes circles instead of arcs
  print("Creating rounded corner shape with bulge =", bulge)
  
  G.setLayer("TEST_rounded_corners")
  G.setThickness(-5)
  
  -- Your original makePartShape call (problematic)
  G.makePartShape({edgeCornerRExist,0},		--chamfered part shape
    {X-edgeCornerRExist,0,0,bulge},
    {X,edgeCornerRExist},
    {X,Y-edgeCornerRExist,0,bulge},
    {X-edgeCornerRExist,Y},
    {edgeCornerRExist,Y,0,bulge},
    {0,Y-edgeCornerRExist},
    {0,edgeCornerRExist,0,bulge},
    {edgeCornerRExist,0})
  
  -- Test 3: Let's try creating the same shape manually with proper bulge handling
  print("Test 3: Manual rounded corners with proper bulge handling")
  
  G.setLayer("TEST_manual_rounded")
  G.setThickness(-5)
  
  -- Create the same shape but with separate point copies
  local corner_points = {
    {edgeCornerRExist, 0, 0, 0},                    -- Start point, no bulge
    {X-edgeCornerRExist, 0, 0, bulge},              -- Bulge to next point
    {X, edgeCornerRExist, 0, 0},                    -- No bulge from this point
    {X, Y-edgeCornerRExist, 0, bulge},              -- Bulge to next point
    {X-edgeCornerRExist, Y, 0, 0},                  -- No bulge from this point
    {edgeCornerRExist, Y, 0, bulge},                -- Bulge to next point
    {0, Y-edgeCornerRExist, 0, 0},                  -- No bulge from this point
    {0, edgeCornerRExist, 0, bulge},                -- Bulge to next point
    {edgeCornerRExist, 0, 0, 0}                     -- Back to start, no bulge
  }
  
  G.polyline(corner_points[1], corner_points[2], corner_points[3], corner_points[4], 
             corner_points[5], corner_points[6], corner_points[7], corner_points[8], corner_points[9])
  
  -- Test 4: Simple arc test to verify our fixes work
  print("Test 4: Simple arc verification")
  
  G.setLayer("TEST_simple_arc")
  G.setThickness(-5)
  
  -- This should create a proper quarter-circle arc
  G.arc({100, 100}, 30, 0, 90, false, "test_quarter_arc")
  
  print("=== Test Complete ===")
  print("Check the visualization:")
  print("- PANEL layer should show default rectangle")
  print("- TEST_rounded_corners should show the problematic version")
  print("- TEST_manual_rounded should show proper rounded corners")
  print("- TEST_simple_arc should show a quarter circle arc")
  
  return true
end

require "ADekoDebugMode"
