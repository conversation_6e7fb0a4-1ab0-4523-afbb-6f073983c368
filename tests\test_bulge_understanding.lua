-- Test script to understand bulge behavior in ADekoLib
-- This script demonstrates the correct way to apply bulge values

function modelMain()
  G = ADekoLib
  
  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end

  G.setThickness(-materialThickness)
  G.setFace("top")
  G.makePartShape()
  
  -- Test 1: Simple arc using line function
  print("=== Test 1: Simple Arc with line() ===")
  <PERSON><PERSON>setLayer("TEST_line_arc")
  G.setThickness(-5)
  
  local p1 = {50, 50}
  local p2 = {150, 50}
  local bulge_value = 0.5  -- Positive bulge creates counter-clockwise arc
  
  G.line(p1, p2, bulge_value)
  
  -- Test 2: Polyline with single arc segment
  print("=== Test 2: Polyline with Arc Segment ===")
  G.setLayer("TEST_polyline_arc")
  G.setThickness(-5)
  
  -- In polyline, bulge is applied to the point where the arc STARTS
  local arc_p1 = {50, 100, 0, bulge_value}  -- bulge applies from this point to next
  local arc_p2 = {150, 100, 0, 0}           -- no bulge from this point
  
  G.polyline(arc_p1, arc_p2)
  
  -- Test 3: Complex polyline with multiple arcs
  print("=== Test 3: Complex <PERSON>yline with Multiple Arcs ===")
  G.setLayer("TEST_complex_polyline")
  G.setThickness(-5)
  
  local complex_points = {
    {50, 150, 0, 0},      -- Start point, no arc
    {100, 150, 0, 0.3},   -- Arc from this point to next (bulge 0.3)
    {150, 180, 0, -0.4},  -- Arc from this point to next (bulge -0.4, clockwise)
    {200, 150, 0, 0.2},   -- Arc from this point to next (bulge 0.2)
    {250, 150, 0, 0}      -- End point, no arc
  }
  
  G.polyline(complex_points[1], complex_points[2], complex_points[3], complex_points[4], complex_points[5])
  
  -- Test 4: Demonstrate the issue in your original code
  print("=== Test 4: Demonstrating the Original Issue ===")
  G.setLayer("TEST_issue_demo")
  G.setThickness(-5)
  
  -- This is similar to what was happening in your code
  local point_a = {50, 200}
  local point_b = {100, 250}
  local point_c = {150, 200}
  
  -- Calculate bulge for arc through these three points
  local calculated_bulge = G.bulge(point_a, point_b, point_c)
  print("Calculated bulge:", calculated_bulge)
  
  -- WRONG way (what was happening before):
  -- point_b[4] = calculated_bulge  -- This modifies the original point
  -- G.polyline(point_a, point_b, point_c)  -- This creates unexpected results
  
  -- CORRECT way:
  local correct_a = {point_a[1], point_a[2], 0, calculated_bulge}  -- Arc starts here
  local correct_b = {point_b[1], point_b[2], 0, 0}                -- No arc from middle point
  local correct_c = {point_c[1], point_c[2], 0, 0}                -- End point
  
  G.polyline(correct_a, correct_b, correct_c)
  
  -- Test 5: Show radius calculation
  print("=== Test 5: Radius Calculation ===")
  local radius = G.radius(point_a, point_c, calculated_bulge)
  print("Radius for the arc:", radius)
  
  -- Draw the center point for reference
  G.setLayer("TEST_centers")
  local comment, center1, center2 = G.circleCircleIntersection(point_a, radius, point_c, radius)
  if comment == "intersection" then
    local center = center1
    if calculated_bulge < 0 then
      center = center2
    end
    G.circle(center, 2)  -- Small circle to mark center
    print("Arc center:", center[1], center[2])
  end
  
  return true
end

require "ADekoDebugMode"
